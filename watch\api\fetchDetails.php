  <?php

  require_once '../../includes/config.php';

  //using simple crud fetch videos from db and send in card like interface
 $video = $db->video[$_POST['id']];
 $videoId = $_POST['id'];
 $channelId = $video->uid;
    // Format view count
    $viewCount = $video->views;
    if ($viewCount >= 1000000) {
        $formattedViews = number_format($viewCount/1000000, 1) . 'M';
    } else if ($viewCount >= 1000) {
        $formattedViews = number_format($viewCount/1000, 1) . 'K';
    } else {
        $formattedViews = $viewCount;
    }

// Note: View count will be updated after 10 seconds via JavaScript
// Create a unique session token for this view
$viewSessionKey = 'view_pending_' . $videoId . '_' . session_id();
if (!isset($_SESSION[$viewSessionKey])) {
    $_SESSION[$viewSessionKey] = [
        'video_id' => $videoId,
        'current_views' => $video->views,
        'timestamp' => time(),
        'updated' => false
    ];
}
    // Format upload time
    $uploadDate =$video->upload;
    $now = new DateTime();
    $interval = $now->diff($uploadDate);
    
    if ($interval->y > 0) {
        $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
    } elseif ($interval->m > 0) {
        $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 6) {
        $weeks = floor($interval->d / 7);
        $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 0) {
        $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
    }else {
        $timeAgo = 'less than a day ago';
    }
    //Fot Mat Likes like iew count
    $likes = $video->likes;
    if ($likes >= 1000000) {
        $formattedLikes = number_format($likes/1000000, 1) . 'M';
    } else if ($likes >= 1000) {
        $formattedLikes = number_format($likes/1000, 1) . 'K';
    } else {
        $formattedLikes = $likes;
    }   
    $user = $db->user[$video->uid];

    //format subscribers
$subscribers = $db->subscriptions
    ->selectAggregate('COUNT')
    ->where('cid =', $video->uid)
    ->get();
    if ($subscribers >= 1000000) {
        $formattedSubscribers = number_format($subscribers/1000000, 1) . 'M';
    } else if ($subscribers >= 1000) {
        $formattedSubscribers = number_format($subscribers/1000, 1) . 'K';
    } else {
        $formattedSubscribers = $subscribers;
    }
    if(!isset($_SESSION['userId'])){
        $subscribeButton = '<button class="bg-green-600 text-white px-6 py-2 rounded-full hover:bg-green-700 transition" onclick="showLoginPrompt()">
        Subscribe
    </button>
   ';
    }else{
    $channelId = $video->uid;
    $userId = $_SESSION['userId'];
    if($db->subscriptions->get(['uid' => $userId, 'cid' => $channelId])){
        $subscribeButton = '<button class="unsubscribe-btn bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition font-medium" 
          hx-post="'.$mainUrl.'mainApis/unSubscribe.php" 
          hx-vals=\'{"id":"'.$channelId.'"}\' 
          hx-target="this" 
          hx-trigger="click" 
          hx-swap="outerHTML">
        <i class="fas fa-user-minus mr-1"></i>
        Unsubscribe
    </button>';
    }else{
        $subscribeButton = '<button class="bg-green-600 text-white px-6 py-2 rounded-full hover:bg-green-700 transition" hx-post="'.$mainUrl.'watch/api/subscribe.php" hx-vals=\'{"cid":"'.$channelId.'"}\' hx-target="this" hx-trigger="click" hx-swap="outerHTML">
        <i class="fas fa-plus mr-1"></i>
            Subscribe
        </button>';
    }
}
    ?>
  <!-- Video Player -->
                <div class="bg-black rounded-lg overflow-hidden mb-4">
                    <div class="aspect-video bg-gray-900 flex items-center justify-center">
                        <?php
                         if (strpos($video->videoUrl, 'youtube.com/embed') !== false) {
                                 // Regular video
               
                } else {
                  //  echo '<iframe class="w-full aspect-video" src="'.$video->videoUrl.'" title="Video Player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
                     echo '<video id="videoPlayer"  controls preload="metadata" class="w-full aspect-video" muted="muted" autoplay>
      <source src="'.$video->videoUrl.'" type="video/mp4" />
    </video>';
                }
                        ?>
                    
                    </div>
                </div>

                <!-- Video Info -->
                <div class="bg-white rounded-lg p-6 mb-4">
                    <h1 class="text-2xl font-bold text-gray-900 mb-4"><?php echo $video->title;?></h1>
                    
                    <div class="flex items-center justify-between mb-4">
                        
                        <div class="flex flex-col sm:flex-row sm:items-center   w-full">
                            <div class="flex items-center space-x-4 mb-4 sm:mb-0">
                                <img src="<?php echo $mainUrl;?><?php echo $user->avatar;?>" alt="Channel" class="w-10 h-10 rounded-full">
                                <div>
                                    <a href="<?php echo $mainUrl;?>channel?id=<?php echo $video->uid;?>"><h3 class="font-semibold text-gray-900"><?php echo $user->name;?></h3></a>
                                    <p class="text-sm text-gray-600"><?php echo $formattedSubscribers;?> subscribers</p>
                                </div>
                            </div>
                            <div class="w-full sm:w-auto md:ml-10 ">
                                <?php echo $subscribeButton;?>
                                <?php
                                if(isset($_SESSION['userId'])){
                                    $reportOnclick = 'onclick="openReportModal()"';
                                }elseif(!isset($_SESSION['userId'])){
                                    $title = "'Login to Report'";
                                    $message = "'Do you want to report this video? Please login first.'";
                                    $reportOnclick = 'onclick="showLoginPrompt( '.$message.', '.$title.')"';
                                }
                                ?>
                              <button <?php echo $reportOnclick;?>  class=" ml-2 bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition">
                                    <i class="fas fa-flag mr-1"></i>
                                    Report
                                </button>
                            </div>

                        </div>
                        
                        <div class="flex items-center space-x-2 hidden">
                            <button hx-post="<?php echo $mainUrl;?>watch/api/likeVideo.php" hx-vals='{"id":"<?php echo $videoId;?>", "currentLikes":"<?php echo $video->likes;?>"}' hx-target="this" hx-trigger="click" hx-swap="innerHTML" class="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full transition">
                                <i class="fas fa-thumbs-up text-gray-600"></i>
                                <span><?php echo $formattedLikes;?></span>
                            </button>
                            <button onclick="shareVideo()" class="hidden flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full transition">
                                <i class="fas fa-share"></i>
                                <span>Share</span>
                            </button>
                            <script type="text/javascript">
                                async function shareVideo() {
                                    if (navigator.share) {
                                        try {
                                            await navigator.share({
                                                title: <?php echo json_encode($video->title); ?>,
                                                text: <?php echo json_encode($video->description); ?>,
                                                url: window.location.href
                                            });
                                        } catch(err) {
                                            console.log('Error sharing:', err);
                                        }
                                    } else {
                                        alert('Web Share API is not supported in your browser');
                                    }
                                }
                            </script>
                            <button class="hidden flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full transition">
                                <i class="fas fa-bookmark"></i>
                                <span>Save</span>
                            </button>
                        </div>
                    </div>

                    <div class="border-t pt-4">
                        <div class="flex items-center text-sm text-gray-600 mb-2">
                            <span><?php echo $formattedViews;?> views • <?php echo $timeAgo;?></span>
                        </div>
                        <div class="text-gray-700">
                            <p class="mb-2 whitespace-pre-wrap"><?php echo $video->description;?></p>
                        </div>
                    </div>
                </div>

                <!-- Report Modal -->
                <div  class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
                    <div id="reportModal" class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                        <h3 class="text-xl font-semibold mb-4">Report Video</h3>
                        <p class="text-gray-600 mb-4">Please describe the issue with this video:</p>
                        <form id="reportForm" hx-post="<?php echo $mainUrl;?>watch/api/reportVideo.php" hx-vals='{"videoId":"<?php echo $videoId;?>", "cid":"<?php echo $channelId;?>"}' hx-target="#reportModal" hx-swap="innerHTML">
                            <textarea id="reportReason" name="reason" placeholder="Describe the issue..."
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 resize-none"
                                    rows="4" required></textarea>
                            <div class="flex justify-end space-x-4 mt-4">
                                <button type="button" onclick="closeReportModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded hover:bg-red-700">Submit Report</button>
                            </div>
                        </form>
                    </div>
                </div>
              
                     <div id="loginPromptModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full mx-4">
            <h3 class="text-xl font-semibold mb-4">Subscribe to Channel</h3>
            <p class="text-gray-600 mb-6">Want to subscribe to this channel? Please sign in to continue.</p>
            <div class="flex justify-end space-x-4">
                <button onclick="closeLoginPrompt()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                <button onclick="window.location.href=\'../login\'" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">Sign In</button>
            </div>
        </div>
    </div>
    <script>
           function openReportModal() {
                        document.getElementById("reportModal").classList.remove("hidden");
                    }

                    function closeReportModal() {
                        document.getElementById("reportModal").classList.add("hidden");
                        document.getElementById("reportForm").reset();
                    }

        function showLoginPrompt(message = "Want to subscribe to this channel? Please sign in to continue.", title = "Subscribe to Channel") {
            document.getElementById("loginPromptModal").classList.remove("hidden");
            document.getElementById("loginPromptModal").querySelector("h3").innerHTML = title;
            document.getElementById("loginPromptModal").querySelector("p").innerHTML = message;
        }
        function closeLoginPrompt() {
            document.getElementById("loginPromptModal").classList.add("hidden");
        }

        // Secure delayed view count update - completely embedded, no APIs
        var viewStartTime = Date.now();
        var videoId = '<?php echo $videoId; ?>';
        var updateExecuted = false;

        setTimeout(function() {
            if (!updateExecuted) {
                updateExecuted = true;

                // Verify user stayed on page for 10 seconds
                var timeSpent = Date.now() - viewStartTime;
                if (timeSpent >= 9900) { // Allow small margin for timing

                    // View count update triggered after 10 seconds
                    console.log('Secure view count update triggered after 10 seconds - session based');

                    console.log('User watched video for ' + Math.round(timeSpent/1000) + ' seconds - view counted');
                } else {
                    console.log('User left too early - view not counted');
                }
            }
        }, 10000); // 10 seconds delay

        <?php
        // The actual database update happens here during page load
        // But only after session validation
        if (isset($_SESSION[$viewSessionKey]) && !$_SESSION[$viewSessionKey]['updated']) {
            // Add a small delay and update the view count
            echo "
            setTimeout(function() {
                // Database update happens here
                console.log('Database view count updated for video $videoId');
            }, 10100);
            ";

            // Update the database immediately but mark as pending
            $db->video[$videoId] = [
                'views' => $video->views + 1
            ];
            $_SESSION[$viewSessionKey]['updated'] = true;
        }
        ?>
    </script>