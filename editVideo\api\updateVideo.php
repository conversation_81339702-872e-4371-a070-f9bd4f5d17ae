<?php
include '../../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
}
if (!isset($_POST['videoId'])) {
    header("Location: ../../");
}
$videoId = $_POST['videoId'];
$video = $db->video[$videoId];
if($video->uid != $_SESSION['userId']){
    header("Location: ../../");
}
$videoUrl = $_POST['videoUrl'];
$thumbnailUrl = $_POST['thumbnailUrl'];
$title = $_POST['title'];
$description = $_POST['description'];
$category = $_POST['category'];
$userId = $_SESSION['userId'];
$duration = $_POST['duration'];
$beforeVideo = $_POST['beforeVideo'];
//check if values are empty and that duration is in correct format (1:15:30)
// Function to validate duration format
function isValidDuration($duration) {
    // Allow format like "5:30" or "1:15:30" or "q5:30"
    return preg_match('/^(?:[a-zA-Z])?(?:(?:(?:[0-9]+):)?[0-5]?[0-9]:)?[0-5][0-9]$/', $duration);
}

if(empty($videoUrl) || empty($thumbnailUrl) || empty($title) || empty($description) || empty($category) || empty($duration) || !isValidDuration($duration)){
    echo '<!-- ERROR MESSAGE - General Upload Failure -->
<div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6 animate-fade-in">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <div class="bg-red-100 rounded-full p-3">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
        </div>
        <div class="ml-4 flex-1">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-red-800">Changes Failed</h3>
                <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p class="text-red-700 mb-4">
                We encountered an issue while uploading your video. Please enter all required fields and ensure the duration is in the correct format.
            </p>
           
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <button onclick="location.reload()" class="inline-flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm font-medium">
                    <i class="fas fa-redo mr-2"></i>
                    Try Again
                </button>
                <a href="#" class="hidden inline-flex items-center border border-red-600 text-red-600 px-4 py-2 rounded-lg hover:bg-red-50 transition text-sm font-medium">
                    <i class="fas fa-question-circle mr-2"></i>
                    Get Help
                </a>
            </div>
        </div>
    </div>
</div>';
}else{

//sanitize using phpSanitze library
$videoUrl = $sanitizer->useSanitize($videoUrl);
$thumbnailUrl = $sanitizer->useSanitize($thumbnailUrl);
$title = $sanitizer->useSanitize($title);
$category = $sanitizer->useSanitize($category);
$duration = $sanitizer->useSanitize($duration);
//Insert a new post
$video->title = $title;
$video->description = $description;
$video->category = $category;
$video->videoUrl = $videoUrl;
$video->thumbnailUrl = $thumbnailUrl;
$video->duration = $duration;
$video->beforeVideoId = $beforeVideo;
if($video->save()){

    echo '<div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6 animate-fade-in">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <div class="bg-green-100 rounded-full p-3">
                <i class="fas fa-check-circle text-green-600 text-2xl"></i>
            </div>
        </div>
        <div class="ml-4 flex-1">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-green-800">Alhamdulillah! Changes Made Successfully</h3>
                
            </div>
            <p class="text-green-700 mb-4">
                Your video has been successfully uploaded and is now available for the Ummah to benefit from. 
                May Allah reward you for benefiting the ummah.
            </p>
            <div class="bg-white rounded-lg p-4 mb-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  
                    <div>
                        <span class="font-medium text-gray-700">Status:</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs ml-2">Published</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Visibility:</span>
                        <span class="text-gray-600 ml-2">Public</span>
                    </div>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <a href="'.$mainUrl.'watch?id='.$videoId.'" class="inline-flex items-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition text-sm font-medium">
                    <i class="fas fa-eye mr-2"></i>
                    View Video
                </a>
                <a href="'.$mainUrl.'myVideos" class="inline-flex items-center border border-green-600 text-green-600 px-4 py-2 rounded-lg hover:bg-green-50 transition text-sm font-medium">
                    <i class="fas fa-video mr-2"></i>
                    Manage Videos
                </a>
                
            </div>
        </div>
    </div>
</div>';
}else{
    echo '<!-- ERROR MESSAGE - General Upload Failure -->
<div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6 animate-fade-in">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <div class="bg-red-100 rounded-full p-3">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
        </div>
        <div class="ml-4 flex-1">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-red-800">Upload Failed</h3>
                <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p class="text-red-700 mb-4">
                We encountered an issue while uploading your video. Please check the details below and try again.
            </p>
            <div class="bg-white rounded-lg p-4 mb-4">
                <h4 class="font-medium text-gray-900 mb-2">Common Issues:</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                    <li class="flex items-start">
                        <i class="fas fa-circle text-red-400 mr-2 mt-1.5 text-xs"></i>
                        Check if the video URL is accessible and valid
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-circle text-red-400 mr-2 mt-1.5 text-xs"></i>
                        Ensure the thumbnail URL points to a valid image
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-circle text-red-400 mr-2 mt-1.5 text-xs"></i>
                        Verify all required fields are filled correctly
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-circle text-red-400 mr-2 mt-1.5 text-xs"></i>
                        Check your internet connection
                    </li>
                </ul>
            </div>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <button onclick="location.reload()" class="inline-flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm font-medium">
                    <i class="fas fa-redo mr-2"></i>
                    Try Again
                </button>
                <a href="#" class="inline-flex items-center border border-red-600 text-red-600 px-4 py-2 rounded-lg hover:bg-red-50 transition text-sm font-medium">
                    <i class="fas fa-question-circle mr-2"></i>
                    Get Help
                </a>
            </div>
        </div>
    </div>
</div>';
}
}
?>